#!/usr/bin/env python3
"""
服务启动脚本
解决相对导入问题
"""
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # 从 scripts 目录回到项目根目录
sys.path.insert(0, project_root)
sys.path.insert(0, current_dir)

# 设置环境变量
os.environ.setdefault("PYTHONPATH", f"{project_root}:{current_dir}")

if __name__ == "__main__":
    # 切换到项目根目录
    os.chdir(project_root)

    print(f"🚀 启动 GuiXiaoXiRag 服务器...")
    print(f"📁 工作目录: {project_root}")

    # 导入并运行主应用
    from main import main
    main()
