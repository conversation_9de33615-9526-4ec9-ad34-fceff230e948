#!/usr/bin/env python3
"""
快速启动脚本
自动检查环境、配置并启动服务
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 12):
        print("❌ Python 3.12+ 是必需的")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version}")
    return True

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import fastapi
        import uvicorn
        print("✅ 核心依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def setup_config():
    """设置配置文件"""
    config_dir = Path("config")
    config_file = config_dir / "config.py"
    example_file = config_dir / "config.example.py"
    
    if not config_file.exists() and example_file.exists():
        print("📝 创建配置文件...")
        shutil.copy(example_file, config_file)
        print("⚠️  请编辑 config/config.py 设置您的 OpenAI API Key")
        return False
    elif config_file.exists():
        print("✅ 配置文件已存在")
        return True
    else:
        print("❌ 配置文件不存在")
        return False

def create_directories():
    """创建必要的目录"""
    dirs = ["logs", "knowledgeBase", "uploads"]
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
    print("✅ 目录结构检查完成")

def start_server():
    """启动服务器"""
    print("🚀 启动服务器...")
    try:
        # 切换到项目根目录
        os.chdir(Path(__file__).parent.parent)
        
        # 启动服务
        subprocess.run([
            sys.executable, "main.py"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🎯 GuiXiaoXiRag 快速启动")
    print("=" * 40)
    
    # 检查环境
    if not check_python_version():
        return
    
    if not check_dependencies():
        return
    
    # 设置配置
    create_directories()
    if not setup_config():
        return
    
    # 启动服务
    start_server()

if __name__ == "__main__":
    main()
