"""
查询处理器 - 基于大模型的意图识别、意图补充、意图修复和内容过滤
优化版本 - 更好的错误处理、性能优化和配置管理
"""
import re
import json
import time
from typing import Dict, List, Optional, Any

from ..common.logging_utils import logger_manager
INTENT_TYPES = {
    "knowledge_query": "知识查询",
    "factual_question": "事实性问题",
    "analytical_question": "分析性问题",
    "procedural_question": "程序性问题",
    "creative_request": "创意请求",
    "greeting": "问候",
    "unclear": "意图不明确",
    "illegal_content": "非法内容"
}
SAFETY_LEVELS = {
    "safe": "安全",
    "suspicious": "可疑",
    "unsafe": "不安全",
    "illegal": "非法"
}


class QueryProcessor:
    """基于大模型的查询处理器"""

    def __init__(self, llm_func=None):
        self.llm_func = llm_func
        self.logger = logger_manager.setup_query_logger()
        self.illegal_keywords = self._load_illegal_keywords()
        self.intent_patterns = self._load_intent_patterns()
        self.enhancement_templates = self._load_enhancement_templates()
        
        # 方向性意图：正向教育/防范 与 负面实施/教程
        self.educational_intent_patterns = [
            "防范", "避免", "识别", "辨别", "举报", "报警", "危害", "风险", "法律后果",
            "合规", "合法", "合规要求", "不良后果", "如何远离", "不该做", "违法与否",
            "how to avoid", "how to report", "how to identify", "risk", "legal consequences"
        ]
        self.instructive_intent_patterns = [
            "实施", "教程", "步骤", "方法", "技巧", "购买", "在哪里买", "获取", "制作",
            "how to", "guide", "step by step", "where to buy", "make", "build"
        ]

        # 大模型提示词模板
        self.safety_check_prompt = """
你是一个内容安全审核专家。请分析以下查询是否涉及违法违规内容。

查询内容："{query}"

请从以下维度进行分析：
1. 是否涉及违法犯罪活动（如赌博、毒品、诈骗、暴力等）
2. 是否涉及不当内容（如色情、仇恨言论等）
3. 查询意图是正向教育/防范，还是负面实施/教程

请以JSON格式返回分析结果：
{{
    "is_safe": true/false,
    "safety_level": "safe/suspicious/unsafe/illegal",
    "risk_factors": ["风险因素1", "风险因素2"],
    "intent_direction": "educational/instructive/neutral",
    "confidence": 0.95,
    "reason": "详细分析原因"
}}

注意：
- 对于"如何防范/识别/举报"等正向教育内容，应标记为安全
- 对于"如何实施/制作/购买"等可能的违法指导，应标记为不安全
- 严格按照JSON格式返回，不要包含其他内容
"""

        self.intent_analysis_prompt = """
你是一个查询意图分析专家。请分析以下查询的具体意图类型。

查询内容："{query}"

意图类型定义：
- knowledge_query: 知识查询（什么是、介绍、解释、定义等）
- factual_question: 事实性问题（谁、何时、哪里、多少等）
- analytical_question: 分析性问题（为什么、如何分析、比较、评价等）
- procedural_question: 程序性问题（步骤、流程、方法、操作等）
- creative_request: 创意请求（创作、写作、设计、生成等）
- greeting: 问候（你好、再见等）
- unclear: 意图不明确

请以JSON格式返回分析结果：
{{
    "intent_type": "knowledge_query",
    "confidence": 0.95,
    "reason": "分析原因",
    "keywords": ["关键词1", "关键词2"]
}}

严格按照JSON格式返回，不要包含其他内容。
"""

        self.query_enhancement_prompt = """
你是一个查询优化专家。请根据查询意图对以下查询进行优化和增强。

原始查询："{query}"
意图类型：{intent_type}
安全级别：{safety_level}

优化原则：
1. 如果是安全的知识查询，可以扩展为更详细、更全面的问题
2. 如果涉及敏感内容，不要进行增强
3. 保持查询的核心意图不变
4. 使查询更加清晰、具体

请以JSON格式返回优化结果：
{{
    "enhanced_query": "优化后的查询",
    "should_enhance": true/false,
    "enhancement_reason": "优化原因",
    "suggestions": ["建议1", "建议2"]
}}

如果不适合增强，请设置should_enhance为false。
严格按照JSON格式返回，不要包含其他内容。
"""

    def _find_illegal_hits(self, query_lower: str) -> List[str]:
        """查找命中的非法关键词"""
        hits: List[str] = []
        for _, keywords in self.illegal_keywords.items():
            for kw in keywords:
                if kw.lower() in query_lower:
                    hits.append(kw)
        # 去重
        return list(dict.fromkeys(hits))

    def _has_educational_intent(self, query_lower: str) -> bool:
        return any(term.lower() in query_lower for term in self.educational_intent_patterns)

    def _has_instructive_intent(self, query_lower: str) -> bool:
        return any(term.lower() in query_lower for term in self.instructive_intent_patterns)
    
    def _load_illegal_keywords(self) -> Dict[str, List[str]]:
        """加载非法关键词库"""
        return {
            "gambling": [
                "赌博", "赌场", "赌钱", "赌注", "博彩", "彩票作弊", "赌球", "赌马",
                "网络赌博", "地下赌场", "赌博网站", "赌博平台", "赌博技巧",
                "gambling", "casino", "betting", "poker", "blackjack"
            ],
            "drugs": [
                "毒品", "吸毒", "贩毒", "制毒", "海洛因", "可卡因", "冰毒", "摇头丸",
                "大麻", "鸦片", "吗啡", "芬太尼", "毒品交易", "毒品制作",
                "drugs", "cocaine", "heroin", "marijuana", "methamphetamine"
            ],
            "pornography": [
                "色情", "黄色", "淫秽", "性交易", "卖淫", "嫖娼", "色情网站",
                "成人内容", "性服务", "色情视频", "裸体", "性爱", "裸聊",
                "pornography", "adult content", "sexual services", "prostitution"
            ],
            "violence": [
                "暴力", "杀人", "谋杀", "自杀", "恐怖主义", "爆炸", "枪支", "武器",
                "伤害他人", "暴力犯罪", "恐怖袭击", "自残", "虐待",
                "violence", "murder", "terrorism", "weapons", "suicide"
            ],
            "fraud": [
                "诈骗", "欺诈", "骗钱", "传销", "非法集资", "洗钱", "假币",
                "信用卡诈骗", "网络诈骗", "电信诈骗", "金融诈骗", "敲诈勒索",
                "fraud", "scam", "money laundering", "pyramid scheme"
            ],
            "illegal_loans": [
                "裸贷", "校园贷", "套路贷", "高利贷", "砍头息", "黑贷", "暴力催收", "软暴力",
                "非法放贷", "非法讨债"
            ],
            "others": [
                "制作炸弹", "如何制爆", "买枪", "如何买枪"
            ]
        }
    
    def _load_intent_patterns(self) -> Dict[str, List[str]]:
        """加载意图识别模式"""
        return {
            "knowledge_query": [
                r"什么是", r"介绍一下", r"解释", r"定义", r"概念",
                r"what is", r"explain", r"define", r"describe"
            ],
            "factual_question": [
                r"谁是", r"何时", r"哪里", r"多少", r"几个",
                r"who is", r"when", r"where", r"how many", r"how much"
            ],
            "analytical_question": [
                r"为什么", r"如何", r"怎样", r"分析", r"比较", r"评价",
                r"why", r"how", r"analyze", r"compare", r"evaluate"
            ],
            "procedural_question": [
                r"步骤", r"流程", r"方法", r"操作", r"教程", r"指南",
                r"steps", r"process", r"method", r"tutorial", r"guide"
            ],
            "creative_request": [
                r"创作", r"写", r"设计", r"生成", r"创造", r"编写",
                r"create", r"write", r"design", r"generate", r"compose"
            ],
            "greeting": [
                r"你好", r"您好", r"早上好", r"晚上好", r"再见",
                r"hello", r"hi", r"good morning", r"good evening", r"goodbye"
            ]
        }
    
    def _load_enhancement_templates(self) -> Dict[str, List[str]]:
        """加载查询增强模板"""
        return {
            "knowledge_query": [
                "请详细解释{query}的概念、特点和应用场景",
                "关于{query}，请提供全面的背景信息和相关知识",
                "请从多个角度分析{query}的重要性和影响"
            ],
            "factual_question": [
                "请提供关于{query}的准确事实信息和数据",
                "关于{query}，请给出具体的时间、地点、人物等详细信息",
                "请列出与{query}相关的关键事实和统计数据"
            ],
            "analytical_question": [
                "请深入分析{query}，包括原因、影响和解决方案",
                "关于{query}，请提供多角度的分析和见解",
                "请系统性地分析{query}的各个方面和相互关系"
            ],
            "procedural_question": [
                "请提供{query}的详细步骤和操作指南",
                "关于{query}，请给出清晰的流程和注意事项",
                "请列出{query}的具体方法和最佳实践"
            ]
        }

    async def process_query(self, query: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """基于大模型的查询处理"""
        start_time = time.time()

        try:
            self.logger.info(f"开始处理查询: {query[:50]}...")

            # 1. 查询清理和标准化
            processed_query = await self._clean_and_normalize_query(query)
            self.logger.debug(f"查询清理完成: {processed_query[:50]}...")

            # 2. 使用大模型进行安全检查
            self.logger.debug("开始安全检查")
            safety_result = await self._llm_safety_check(processed_query)
            self.logger.info(f"安全检查完成: {safety_result.get('safety_level', 'unknown')}, 安全: {safety_result.get('is_safe', False)}")

            # 检查安全结果格式并统一处理
            is_safe = safety_result.get("is_safe")
            if is_safe is None:
                # 兼容旧格式，通过should_reject判断
                is_safe = not safety_result.get("should_reject", False)

            # 如果不安全，直接返回拒绝结果
            if not is_safe:
                self.logger.info("查询被标记为不安全，返回拒绝结果")
                safety_tips = [
                    "请遵守法律法规与平台使用规范，不要寻求或传播违法内容",
                    "若遇到疑似非法行为，建议保留证据并向相关机构举报",
                    "注意个人隐私与财产安全，避免卷入高风险行为"
                ]
                safe_alternatives = await self._generate_safe_alternatives(processed_query)

                # 处理安全级别
                safety_level_str = safety_result.get("safety_level", "unsafe")

                return {
                    "original_query": query,
                    "processed_query": processed_query,
                    "intent_type": "illegal_content",
                    "safety_level": safety_level_str,
                    "confidence": safety_result.get("confidence", 0.9),
                    "suggestions": [],
                    "risk_factors": safety_result.get("risk_factors", []),
                    "enhanced_query": None,
                    "should_reject": True,
                    "rejection_reason": safety_result.get("reason", "查询涉及不当内容"),
                    "safety_tips": safety_tips,
                    "safe_alternatives": safe_alternatives,
                    "processing_time": time.time() - start_time
                }

            # 3. 使用大模型进行意图分析
            self.logger.debug("开始意图分析")
            intent_result = await self._llm_intent_analysis(processed_query)
            self.logger.info(f"意图分析完成: {intent_result.get('intent_type', 'unknown')}")

            # 4. 查询增强（如果适合）
            enhanced_query = None
            if safety_result.get("is_safe", True):
                self.logger.debug("开始查询增强")
                enhancement_result = await self._llm_query_enhancement(
                    processed_query,
                    intent_result.get("intent_type", "unclear"),
                    safety_result.get("safety_level", "safe")
                )
                if enhancement_result.get("should_enhance", False):
                    enhanced_query = enhancement_result.get("enhanced_query")
                    self.logger.info(f"查询增强完成: {enhanced_query[:50] if enhanced_query else 'None'}...")

            # 5. 生成改进建议
            suggestions = await self._generate_suggestions(
                processed_query,
                intent_result.get("intent_type", "unclear"),
                safety_result.get("safety_level", "safe")
            )

            processing_time = time.time() - start_time
            self.logger.info(f"查询处理完成，总耗时: {processing_time:.2f}s")

            return {
                "original_query": query,
                "processed_query": processed_query,
                "intent_type": intent_result.get("intent_type", "unclear"),
                "safety_level": safety_result.get("safety_level", "safe"),
                "confidence": min(
                    safety_result.get("confidence", 0.9),
                    intent_result.get("confidence", 0.9)
                ),
                "suggestions": suggestions,
                "risk_factors": safety_result.get("risk_factors", []),
                "enhanced_query": enhanced_query,
                "should_reject": False,
                "rejection_reason": None,
                "safety_tips": [],
                "safe_alternatives": [],
                "processing_time": processing_time
            }

        except Exception as e:
            self.logger.error(f"查询处理失败: {str(e)}", exc_info=True)
            return {
                "original_query": query,
                "processed_query": query,
                "intent_type": "unclear",
                "safety_level": "suspicious",
                "confidence": 0.0,
                "suggestions": ["查询处理出现错误，请重试"],
                "risk_factors": ["处理异常"],
                "enhanced_query": None,
                "should_reject": True,
                "rejection_reason": "查询处理异常",
                "safety_tips": [],
                "safe_alternatives": [],
                "processing_time": time.time() - start_time
            }

    async def _clean_and_normalize_query(self, query: str) -> str:
        """清理和标准化查询"""
        # 移除多余空白
        query = re.sub(r'\s+', ' ', query.strip())

        # 移除特殊字符（保留基本标点）
        query = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()[\]{}"\'-]', '', query)

        return query

    async def _llm_safety_check(self, query: str) -> Dict[str, Any]:
        """使用大模型进行安全检查"""
        if not self.llm_func:
            # 如果没有LLM函数，使用基础规则检查
            return await self._basic_safety_check(query)

        try:
            prompt = self.safety_check_prompt.format(query=query)
            response = await self.llm_func(prompt)

            # 解析JSON响应
            try:
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                self.logger.warning(f"LLM安全检查响应格式错误: {response}")
                return await self._basic_safety_check(query)

        except Exception as e:
            self.logger.error(f"LLM安全检查失败: {str(e)}")
            return await self._basic_safety_check(query)

    async def _basic_safety_check(self, query: str) -> Dict[str, Any]:
        """基础安全检查（规则基础）"""
        query_lower = query.lower()
        illegal_hits = self._find_illegal_hits(query_lower)

        if illegal_hits:
            # 检查是否有教育意图
            has_educational = self._has_educational_intent(query_lower)
            has_instructive = self._has_instructive_intent(query_lower)

            if has_educational and not has_instructive:
                # 正向教育内容，标记为安全
                return {
                    "is_safe": True,
                    "safety_level": "safe",
                    "risk_factors": [],
                    "intent_direction": "educational",
                    "confidence": 0.8,
                    "reason": "检测到正向教育意图"
                }
            else:
                # 可能的违法指导，标记为不安全
                return {
                    "is_safe": False,
                    "safety_level": "illegal" if len(illegal_hits) > 2 else "unsafe",
                    "risk_factors": illegal_hits,
                    "intent_direction": "instructive" if has_instructive else "neutral",
                    "confidence": 0.9,
                    "reason": f"检测到敏感关键词: {', '.join(illegal_hits)}"
                }

        return {
            "is_safe": True,
            "safety_level": "safe",
            "risk_factors": [],
            "intent_direction": "neutral",
            "confidence": 0.7,
            "reason": "未检测到敏感内容"
        }

    async def _llm_intent_analysis(self, query: str) -> Dict[str, Any]:
        """使用大模型进行意图分析"""
        if not self.llm_func:
            return await self._basic_intent_analysis(query)

        try:
            prompt = self.intent_analysis_prompt.format(query=query)
            response = await self.llm_func(prompt)

            try:
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                self.logger.warning(f"LLM意图分析响应格式错误: {response}")
                return await self._basic_intent_analysis(query)

        except Exception as e:
            self.logger.error(f"LLM意图分析失败: {str(e)}")
            return await self._basic_intent_analysis(query)

    async def _basic_intent_analysis(self, query: str) -> Dict[str, Any]:
        """基础意图分析（规则基础）"""
        query_lower = query.lower()

        for intent_type, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return {
                        "intent_type": intent_type,
                        "confidence": 0.7,
                        "reason": f"匹配模式: {pattern}",
                        "keywords": [pattern]
                    }

        return {
            "intent_type": "unclear",
            "confidence": 0.5,
            "reason": "未匹配到明确意图模式",
            "keywords": []
        }

    async def _llm_query_enhancement(self, query: str, intent_type: str, safety_level: str) -> Dict[str, Any]:
        """使用大模型进行查询增强"""
        if not self.llm_func or safety_level not in ["safe", "suspicious"]:
            return {"should_enhance": False, "enhanced_query": None}

        try:
            prompt = self.query_enhancement_prompt.format(
                query=query,
                intent_type=intent_type,
                safety_level=safety_level
            )
            response = await self.llm_func(prompt)

            try:
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                self.logger.warning(f"LLM查询增强响应格式错误: {response}")
                return {"should_enhance": False, "enhanced_query": None}

        except Exception as e:
            self.logger.error(f"LLM查询增强失败: {str(e)}")
            return {"should_enhance": False, "enhanced_query": None}

    async def _generate_suggestions(self, query: str, intent_type: str, safety_level: str) -> List[str]:
        """生成改进建议"""
        suggestions = []

        if safety_level == "suspicious":
            suggestions.append("建议重新表述查询，避免使用可能引起歧义的词汇")

        if intent_type == "unclear":
            suggestions.append("建议使用更具体、更明确的表述")
            suggestions.append("可以尝试添加更多上下文信息")

        if intent_type == "procedural_question":
            suggestions.append("可以询问具体的步骤或操作流程")
            suggestions.append("建议明确指出需要解决的具体问题")

        if len(query) < 10:
            suggestions.append("建议提供更详细的查询内容")

        return suggestions

    async def _generate_safe_alternatives(self, query: str) -> List[str]:
        """生成安全的替代查询建议"""
        alternatives = [
            "如何识别和防范相关风险？",
            "相关法律法规有哪些规定？",
            "如何举报相关违法行为？",
            "这类行为可能带来什么法律后果？"
        ]

        # 根据查询内容生成更具体的建议
        query_lower = query.lower()

        if any(word in query_lower for word in ["赌博", "gambling"]):
            alternatives.extend([
                "如何识别网络赌博陷阱？",
                "赌博成瘾如何寻求帮助？"
            ])

        if any(word in query_lower for word in ["毒品", "drugs"]):
            alternatives.extend([
                "如何识别毒品的危害？",
                "毒品预防教育的重要性"
            ])

        return alternatives[:3]  # 返回最多3个建议


# 导出查询处理器
__all__ = ["QueryProcessor"]
