#!/usr/bin/env python3
"""
GuiXiaoXiRag 新服务器启动脚本
"""
import sys
import os
import subprocess
from pathlib import Path

def get_project_root():
    """获取项目根目录"""
    return Path(__file__).parent.resolve()

# def get_server_new_path():
#     """获取 server_new 目录路径"""
#     return get_project_root() / "server_new"

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查 Python 版本
    if sys.version_info < (3, 8):
        print(f"❌ Python 版本过低: {sys.version}")
        print("   需要 Python 3.8+")
        return False
    
    print(f"✅ Python 版本: {sys.version}")
    
    # 检查 server_new 目录
    # server_new_path = get_server_new_path()
    # if not server_new_path.exists():
    #     print(f"❌ server_new 目录不存在: {server_new_path}")
    #     return False
    
    # print(f"✅ server_new 目录: {server_new_path}")
    
    # 获取工作目录
    work_path = get_project_root()
    
    # 检查 main.py 文件
    main_py = work_path / "main.py"
    if not main_py.exists():
        print(f"❌ main.py 文件不存在: {main_py}")
        return False
    
    print(f"✅ main.py 文件: {main_py}")
    
    return True

def check_dependencies():
    """检查依赖"""
    print("📦 检查依赖...")
    
    try:
        import fastapi
        print(f"✅ FastAPI: {fastapi.__version__}")
    except ImportError:
        print("❌ FastAPI 未安装")
        return False
    
    try:
        import uvicorn
        print(f"✅ Uvicorn: {uvicorn.__version__}")
    except ImportError:
        print("❌ Uvicorn 未安装")
        return False
    
    return True

def setup_environment():
    """设置环境"""
    print("⚙️  设置环境...")
    
    project_root = get_project_root()
    # server_new_path = get_server_new_path()
    
    # 添加路径到 Python 路径
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # if str(server_new_path) not in sys.path:
    #     sys.path.insert(0, str(server_new_path))
    
    # 设置环境变量
    os.environ.setdefault("PYTHONPATH", f"{project_root}:{project_root}")
    
    # 切换到 server_new 目录
    # os.chdir(server_new_path)
    
    print(f"✅ 工作目录: {os.getcwd()}")
    print(f"✅ Python 路径已设置")

def check_config():
    """检查配置文件"""
    print("📋 检查配置...")
    
    project_root = get_project_root()
    env_file = project_root / ".env"
    env_example = project_root / ".env.example"
    
    if env_file.exists():
        print(f"✅ 配置文件: {env_file}")
    elif env_example.exists():
        print(f"⚠️  未找到 .env 文件，但找到了 .env.example")
        print(f"💡 建议复制 {env_example} 为 {env_file} 并修改配置")
    else:
        print("⚠️  未找到配置文件，将使用默认配置")
    
    return True

def start_server():
    """启动服务器"""
    print("🚀 启动 GuiXiaoXiRag 服务器...")
    print("=" * 50)

    try:
        # 使用 uvicorn 直接启动，避免导入问题
        import subprocess
        project_root = get_project_root()
        # server_new_path = get_server_new_path()

        # 设置环境变量
        env = os.environ.copy()
        env["PYTHONPATH"] = f"{project_root}:{project_root}"

        # 使用 uvicorn 启动
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8002",
            "--reload"
        ]

        print(f"💡 启动命令: {' '.join(cmd)}")
        print("\n📚 API 文档地址:")
        print("   - Swagger UI: http://localhost:8002/docs")
        print("   - ReDoc: http://localhost:8002/redoc")
        print("   - OpenAPI JSON: http://localhost:8002/openapi.json")
        print("=" * 50)

        result = subprocess.run(cmd, cwd=project_root, env=env)

        if result.returncode != 0:
            print(f"❌ 服务器退出，返回码: {result.returncode}")
            return False

    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True

def main():
    """主函数"""
    print("🎯 GuiXiaoXiRag 新服务器启动器")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败")
        sys.exit(1)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败")
        print("💡 请运行: pip install -r requirements.txt")
        sys.exit(1)
    
    # 检查配置
    if not check_config():
        print("\n❌ 配置检查失败")
        sys.exit(1)
    
    # 设置环境
    setup_environment()
    
    # 启动服务器
    if not start_server():
        sys.exit(1)

if __name__ == "__main__":
    main()
